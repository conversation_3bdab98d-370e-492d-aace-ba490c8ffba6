package pl.lazicki.ksystem

import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.content.pm.PackageManager
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.IOException
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.sqrt

private const val LOG_TAG = "AudioRecordingService"
private const val NOTIFICATION_ID = 1
private const val CHANNEL_ID = "AudioRecordingChannel"
private const val SILENCE_TIMEOUT = 5 * 60 * 1000L // 5 minut w milisekundach
private const val SEGMENT_DURATION = 5 * 60 * 1000L // 5 minut na segment
private const val AUDIO_THRESHOLD = 600 // Próg głośności do wykrycia mowy

class AudioRecordingService : Service() {

    private val binder = AudioRecordingBinder()
    
    private val _state = MutableStateFlow(MainState())
    val state: StateFlow<MainState> = _state.asStateFlow()
    
    private var currentFileName: String = ""
    private var recorder: MediaRecorder? = null
    private var audioRecord: AudioRecord? = null
    private var currentSegment = 0
    private var recordingStartTime = 0L
    private var lastVoiceDetected = 0L
    
    private var isListening = false
        set(value) {
            field = value
            _state.value = _state.value.copy(isListening = value)
            updateNotification()
        }
    
    private var isRecording = false
        set(value) {
            field = value
            _state.value = _state.value.copy(isRecording = value)
            updateNotification()
        }

    // Coroutines
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var listeningJob: Job? = null
    private var silenceTimeoutJob: Job? = null
    private var segmentTimerJob: Job? = null

    inner class AudioRecordingBinder : Binder() {
        fun getService(): AudioRecordingService = this@AudioRecordingService
    }

    override fun onBind(intent: Intent): IBinder {
        return binder
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        Log.d(LOG_TAG, "AudioRecordingService created")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_RECORDING -> startVoiceActivatedRecording()
            ACTION_STOP_RECORDING -> stopVoiceActivatedRecording()
            ACTION_STOP_SERVICE -> stopSelf()
        }
        return START_STICKY
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Audio Recording Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Kanał dla serwisu nagrywania audio"
                setSound(null, null)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val stopIntent = Intent(this, AudioRecordingService::class.java).apply {
            action = ACTION_STOP_SERVICE
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 0, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val statusText = when {
            isRecording -> "Nagrywanie segmentu $currentSegment"
            isListening -> "Nasłuchiwanie głosu..."
            else -> "Gotowy do nagrywania"
        }

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("KSystem Audio Recorder")
            .setContentText(statusText)
            .setSmallIcon(android.R.drawable.ic_btn_speak_now)
            .setContentIntent(pendingIntent)
            .addAction(android.R.drawable.ic_media_pause, "Stop", stopPendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .build()
    }

    private fun updateNotification() {
        val notification = createNotification()
        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    fun startVoiceActivatedRecording() {
        if (isListening) return

        startForeground(NOTIFICATION_ID, createNotification())
        
        isListening = true
        currentSegment = 0
        Log.d(LOG_TAG, "Starting voice activation listening...")

        val sampleRate = 44100
        val channelConfig = AudioFormat.CHANNEL_IN_MONO
        val audioFormat = AudioFormat.ENCODING_PCM_16BIT
        val bufferSize = AudioRecord.getMinBufferSize(sampleRate, channelConfig, audioFormat)

        try {
            if (ActivityCompat.checkSelfPermission(
                    this,
                    Manifest.permission.RECORD_AUDIO
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                Log.e(LOG_TAG, "No audio recording permission")
                return
            }

            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.MIC,
                sampleRate,
                channelConfig,
                audioFormat,
                bufferSize
            )

            audioRecord?.startRecording()

            listeningJob = scope.launch(Dispatchers.IO) {
                val buffer = ShortArray(bufferSize)

                while (isListening && audioRecord?.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                    val bytesRead = audioRecord?.read(buffer, 0, bufferSize) ?: 0

                    if (bytesRead > 0) {
                        val amplitude = calculateRMS(buffer, bytesRead)

                        if (amplitude > AUDIO_THRESHOLD) {
                            Log.d(LOG_TAG, "Voice detected! Amplitude: $amplitude")
                            lastVoiceDetected = System.currentTimeMillis()

                            if (!isRecording) {
                                withContext(Dispatchers.Main) {
                                    startRecording()
                                }
                            }

                            resetSilenceTimeout()
                        }
                    }

                    if (isRecording && (System.currentTimeMillis() - lastVoiceDetected) > SILENCE_TIMEOUT) {
                        Log.d(LOG_TAG, "5 minutes of silence detected, stopping recording session")
                        withContext(Dispatchers.Main) {
                            stopRecordingSession()
                        }
                    }

                    delay(100)
                }
            }
        } catch (e: SecurityException) {
            Log.e(LOG_TAG, "Security exception: ${e.message}")
        }
    }

    fun stopVoiceActivatedRecording() {
        isListening = false
        listeningJob?.cancel()
        silenceTimeoutJob?.cancel()
        segmentTimerJob?.cancel()

        audioRecord?.apply {
            if (recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                stop()
            }
            release()
        }
        audioRecord = null

        if (isRecording) {
            stopCurrentRecording()
        }

        Log.d(LOG_TAG, "Voice activation stopped")
        stopForeground(STOP_FOREGROUND_REMOVE)
    }

    private fun generateFileName(segmentNumber: Int): String {
        val name = LocalDateTime.now()
        val formatter = DateTimeFormatter.ofPattern("ddMM_HH.mm")
        val formattedName = name.format(formatter)
        return "${externalCacheDir?.absolutePath}/${formattedName}_seg${segmentNumber}.3gp"
    }

    private fun calculateRMS(buffer: ShortArray, length: Int): Double {
        var sum = 0.0
        for (i in 0 until length) {
            sum += buffer[i] * buffer[i]
        }
        return sqrt(sum / length)
    }

    private fun resetSilenceTimeout() {
        silenceTimeoutJob?.cancel()
    }

    private fun startSegmentTimer() {
        segmentTimerJob?.cancel()
        segmentTimerJob = scope.launch {
            delay(SEGMENT_DURATION)
            Log.d(LOG_TAG, "5-minute segment completed, starting new segment")

            if (isRecording) {
                stopCurrentRecording()
                delay(100)
                startRecording()
            }
        }
    }

    private fun startRecording() {
        if (isRecording) return

        currentSegment++
        currentFileName = generateFileName(currentSegment)
        isRecording = true
        recordingStartTime = System.currentTimeMillis()
        lastVoiceDetected = System.currentTimeMillis()

        Log.d(LOG_TAG, "Starting recording segment $currentSegment to file: $currentFileName")

        recorder = MediaRecorder().apply {
            setAudioSource(MediaRecorder.AudioSource.MIC)
            setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP)
            setOutputFile(currentFileName)
            setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB)

            try {
                prepare()
                start()
                Log.d(LOG_TAG, "Recording started for segment $currentSegment")
                startSegmentTimer()
            } catch (e: IOException) {
                Log.e(LOG_TAG, "prepare() failed: ${e.message}")
                isRecording = false
                currentSegment--
            }
        }
    }

    private fun stopCurrentRecording() {
        if (!isRecording) return

        isRecording = false
        segmentTimerJob?.cancel()

        recorder?.apply {
            try {
                stop()
                release()
                Log.d(LOG_TAG, "Recording segment $currentSegment stopped")
            } catch (e: RuntimeException) {
                Log.e(LOG_TAG, "Error stopping recorder: ${e.message}")
            }
        }
        recorder = null
    }

    private fun stopRecordingSession() {
        stopCurrentRecording()
        Log.d(LOG_TAG, "Recording session completed. Total segments: $currentSegment")
        Log.d(LOG_TAG, "Returning to voice detection mode...")
        currentSegment = 0
    }

    override fun onDestroy() {
        super.onDestroy()
        stopVoiceActivatedRecording()
        scope.cancel()
        Log.d(LOG_TAG, "AudioRecordingService destroyed")
    }

    companion object {
        const val ACTION_START_RECORDING = "pl.lazicki.ksystem.START_RECORDING"
        const val ACTION_STOP_RECORDING = "pl.lazicki.ksystem.STOP_RECORDING"
        const val ACTION_STOP_SERVICE = "pl.lazicki.ksystem.STOP_SERVICE"
    }
}
