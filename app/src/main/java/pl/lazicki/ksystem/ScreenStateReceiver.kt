package pl.lazicki.ksystem

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.util.Log

class ScreenStateReceiver(private val onScreenStateChanged: (Boolean) -> Unit) : BroadcastReceiver() {
    
    private var isRegistered = false
    
    override fun onReceive(context: Context?, intent: Intent?) {
        when (intent?.action) {
            Intent.ACTION_SCREEN_OFF -> {
                Log.d("ScreenStateReceiver", "Screen turned OFF")
                onScreenStateChanged(false)
            }
            Intent.ACTION_SCREEN_ON -> {
                Log.d("ScreenStateReceiver", "Screen turned ON")
                onScreenStateChanged(true)
            }
            Intent.ACTION_USER_PRESENT -> {
                Log.d("ScreenStateReceiver", "User unlocked device")
                onScreenStateChanged(true)
            }
        }
    }
    
    fun register(context: Context) {
        if (!isRegistered) {
            val filter = IntentFilter().apply {
                addAction(Intent.ACTION_SCREEN_OFF)
                addAction(Intent.ACTION_SCREEN_ON)
                addAction(Intent.ACTION_USER_PRESENT)
            }
            context.registerReceiver(this, filter)
            isRegistered = true
            Log.d("ScreenStateReceiver", "Screen state receiver registered")
        }
    }
    
    fun unregister(context: Context) {
        if (isRegistered) {
            try {
                context.unregisterReceiver(this)
                isRegistered = false
                Log.d("ScreenStateReceiver", "Screen state receiver unregistered")
            } catch (e: IllegalArgumentException) {
                Log.w("ScreenStateReceiver", "Receiver was not registered: ${e.message}")
            }
        }
    }
}
