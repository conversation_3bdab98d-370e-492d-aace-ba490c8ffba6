package pl.lazicki.ksystem

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class AudioRecordingServiceConnection(private val context: Context) {

    private val _isServiceConnected = MutableStateFlow(false)
    val isServiceConnected: StateFlow<Boolean> = _isServiceConnected.asStateFlow()
    
    private var audioRecordingService: AudioRecordingService? = null
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Log.d("ServiceConnection", "Service connected")
            val binder = service as AudioRecordingService.AudioRecordingBinder
            audioRecordingService = binder.getService()
            _isServiceConnected.value = true
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Log.d("ServiceConnection", "Service disconnected")
            audioRecordingService = null
            _isServiceConnected.value = false
        }
    }
    
    fun bindToService() {
        val intent = Intent(context, AudioRecordingService::class.java)
        context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
    }
    
    fun unbindFromService() {
        if (_isServiceConnected.value) {
            context.unbindService(serviceConnection)
            _isServiceConnected.value = false
        }
    }
    
    fun startRecording() {
        audioRecordingService?.startVoiceActivatedRecording()
            ?: run {
                // Jeśli serwis nie jest połączony, uruchom go przez Intent
                val intent = Intent(context, AudioRecordingService::class.java).apply {
                    action = AudioRecordingService.ACTION_START_RECORDING
                }
                context.startForegroundService(intent)
            }
    }
    
    fun stopRecording() {
        audioRecordingService?.stopVoiceActivatedRecording()
            ?: run {
                // Jeśli serwis nie jest połączony, zatrzymaj go przez Intent
                val intent = Intent(context, AudioRecordingService::class.java).apply {
                    action = AudioRecordingService.ACTION_STOP_RECORDING
                }
                context.startService(intent)
            }
    }
    
    fun stopService() {
        val intent = Intent(context, AudioRecordingService::class.java).apply {
            action = AudioRecordingService.ACTION_STOP_SERVICE
        }
        context.startService(intent)
        unbindFromService()
    }
    
    fun getServiceStateFlow(): StateFlow<MainState>? {
        return audioRecordingService?.state
    }
}
