package pl.lazicki.ksystem

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.app.ActivityCompat
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import pl.lazicki.ksystem.ui.theme.KSystemTheme

private const val LOG_TAG = "MainActivity"
private const val REQUEST_RECORD_AUDIO_PERMISSION = 200

class MainActivity : ComponentActivity() {

    private lateinit var serviceConnection: AudioRecordingServiceConnection

    // Requesting permission to RECORD_AUDIO
    private var permissionToRecordAccepted = false
    private var permissions: Array<String> = arrayOf(Manifest.permission.RECORD_AUDIO)

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        permissionToRecordAccepted = if (requestCode == REQUEST_RECORD_AUDIO_PERMISSION) {
            grantResults[0] == PackageManager.PERMISSION_GRANTED
        } else {
            false
        }
        if (!permissionToRecordAccepted) finish()
    }

    private fun onRecord(start: Boolean) = if (start) {
        serviceConnection.startRecording()
    } else {
        serviceConnection.stopRecording()
    }





    override fun onStart() {
        super.onStart()
        serviceConnection.bindToService()
    }

    override fun onStop() {
        super.onStop()
        serviceConnection.unbindFromService()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        serviceConnection = AudioRecordingServiceConnection(this)
        ActivityCompat.requestPermissions(this, permissions, REQUEST_RECORD_AUDIO_PERMISSION)

        setContent {
            KSystemTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->

                    val isConnected by serviceConnection.isServiceConnected.collectAsStateWithLifecycle()
                    var serviceState by remember { mutableStateOf(MainState()) }

                    LaunchedEffect(isConnected) {
                        if (isConnected) {
                            serviceConnection.getServiceStateFlow()?.collect { state ->
                                serviceState = state
                            }
                        }
                    }

                    Column {
                        Greeting(
                            modifier = Modifier.padding(innerPadding)
                        )

                        RecordButton(
                            onRecord = { onRecord(it) },
                            isListening = serviceState.isListening,
                            isRecording = serviceState.isRecording,
                            isConnected = isConnected
                        )

                        Text(
                            text = "Service Connected: $isConnected\nisListening: ${serviceState.isListening}\nisRecording: ${serviceState.isRecording}",
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun RecordButton(
    onRecord: (Boolean) -> Unit,
    isListening: Boolean,
    isRecording: Boolean,
    isConnected: Boolean,
    modifier: Modifier = Modifier
) {
    var isActive by remember { mutableStateOf(false) }

    Button(
        onClick = {
            isActive = !isActive
            onRecord(isActive)
        },
        enabled = isConnected,
        modifier = modifier
    ) {
        Text(
            text = when {
                !isConnected -> "Connecting to service..."
                isActive && isRecording -> "Recording - Tap to stop"
                isActive && isListening -> "Listening for voice..."
                isActive -> "Starting voice detection..."
                else -> "Start voice detection"
            }
        )
    }
}

@Composable
fun Greeting(modifier: Modifier = Modifier) {
    Text(
        text = "KSystem test app",
        modifier = modifier
    )
}

@Preview(showBackground = true)
@Composable
fun GreetingPreview() {
    KSystemTheme {
        Greeting()
    }
}