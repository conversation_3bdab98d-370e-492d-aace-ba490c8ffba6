package pl.lazicki.ksystem

import android.Manifest
import android.content.pm.PackageManager
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaPlayer
import android.media.MediaRecorder
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.app.ActivityCompat
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import pl.lazicki.ksystem.ui.theme.KSystemTheme
import java.io.IOException
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.sqrt

private const val LOG_TAG = "AudioRecordTest"
private const val REQUEST_RECORD_AUDIO_PERMISSION = 200
private const val SILENCE_TIMEOUT = 5 * 60 * 1000L // 5 minut w milisekundach
private const val SEGMENT_DURATION = 5 * 60 * 1000L // 5 minut na segment
private const val AUDIO_THRESHOLD = 600 // Próg głośności do wykrycia mowy

class MainActivity : ComponentActivity() {

    private val _state = MutableStateFlow(MainState())
    val state: StateFlow<MainState> = _state.asStateFlow()
    private var currentFileName: String = ""
    private var recorder: MediaRecorder? = null
    private var player: MediaPlayer? = null
    private var audioRecord: AudioRecord? = null
    private var isListening = false
        set(value) {
            field = value
            _state.value = _state.value.copy(isListening = value)
        }
    private var isRecording = false
    set(value) {
        field = value
        _state.value = _state.value.copy(isRecording = value)
    }
    private var currentSegment = 0
    private var recordingStartTime = 0L
    private var lastVoiceDetected = 0L

    // Coroutines
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var listeningJob: Job? = null
    private var silenceTimeoutJob: Job? = null
    private var segmentTimerJob: Job? = null

    // Requesting permission to RECORD_AUDIO
    private var permissionToRecordAccepted = false
    private var permissions: Array<String> = arrayOf(Manifest.permission.RECORD_AUDIO)

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        permissionToRecordAccepted = if (requestCode == REQUEST_RECORD_AUDIO_PERMISSION) {
            grantResults[0] == PackageManager.PERMISSION_GRANTED
        } else {
            false
        }
        if (!permissionToRecordAccepted) finish()
    }

    private fun generateFileName(segmentNumber: Int): String {
        val name = LocalDateTime.now()
        val formatter = DateTimeFormatter.ofPattern("ddMM_HH.mm")
        val formattedName = name.format(formatter)
        return "${externalCacheDir?.absolutePath}/${formattedName}_seg${segmentNumber}.3gp"
    }

    private fun startVoiceActivatedRecording() {
        if (isListening) return

        isListening = true
        currentSegment = 0
        Log.d(LOG_TAG, "Starting voice activation listening...")

        val sampleRate = 44100
        val channelConfig = AudioFormat.CHANNEL_IN_MONO
        val audioFormat = AudioFormat.ENCODING_PCM_16BIT
        val bufferSize = AudioRecord.getMinBufferSize(sampleRate, channelConfig, audioFormat)

        try {
            audioRecord = AudioRecord(
                android.media.MediaRecorder.AudioSource.MIC,
                sampleRate,
                channelConfig,
                audioFormat,
                bufferSize
            )

            audioRecord?.startRecording()

            listeningJob = scope.launch(Dispatchers.IO) {
                val buffer = ShortArray(bufferSize)

                while (isListening && audioRecord?.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                    val bytesRead = audioRecord?.read(buffer, 0, bufferSize) ?: 0

                    if (bytesRead > 0) {
                        val amplitude = calculateRMS(buffer, bytesRead)

                        if (amplitude > AUDIO_THRESHOLD) {
                            Log.d(LOG_TAG, "Voice detected! Amplitude: $amplitude")
                            lastVoiceDetected = System.currentTimeMillis()

                            if (!isRecording) {
                                withContext(Dispatchers.Main) {
                                    startRecording()
                                }
                            }

                            // Reset timeout gdy wykryto dźwięk
                            resetSilenceTimeout()
                        }
                    }

                    // Sprawdź czy minęło 5 minut bez głosu podczas nagrywania
                    if (isRecording && (System.currentTimeMillis() - lastVoiceDetected) > SILENCE_TIMEOUT) {
                        Log.d(LOG_TAG, "5 minutes of silence detected, stopping recording session")
                        withContext(Dispatchers.Main) {
                            stopRecordingSession()
                        }
                    }

                    delay(100) // Sprawdzaj co 100ms
                }
            }
        } catch (e: SecurityException) {
            Log.e(LOG_TAG, "Security exception: ${e.message}")
        }
    }

    private fun stopVoiceActivatedRecording() {
        isListening = false
        listeningJob?.cancel()
        silenceTimeoutJob?.cancel()
        segmentTimerJob?.cancel()

        audioRecord?.apply {
            if (recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                stop()
            }
            release()
        }
        audioRecord = null

        if (isRecording) {
            stopCurrentRecording()
        }

        Log.d(LOG_TAG, "Voice activation stopped")
    }

    private fun resetSilenceTimeout() {
        silenceTimeoutJob?.cancel()
    }

    private fun startSegmentTimer() {
        segmentTimerJob?.cancel()
        segmentTimerJob = scope.launch {
            delay(SEGMENT_DURATION)
            Log.d(LOG_TAG, "5-minute segment completed, starting new segment")

            if (isRecording) {
                // Zatrzymaj obecne nagrywanie
                stopCurrentRecording()

                // Rozpocznij nowy segment
                delay(100) // Krótka przerwa między segmentami
                startRecording()
            }
        }
    }

    private fun calculateRMS(buffer: ShortArray, length: Int): Double {
        var sum = 0.0
        for (i in 0 until length) {
            sum += buffer[i] * buffer[i]
        }
        return sqrt(sum / length)
    }

    private fun onRecord(start: Boolean) = if (start) {
        startVoiceActivatedRecording()
    } else {
        stopVoiceActivatedRecording()
    }

    private fun onPlay(start: Boolean) = if (start) {
        startPlaying()
    } else {
        stopPlaying()
    }

    private fun startPlaying() {
        // Odtwarzaj ostatni nagrany segment
        val playFileName = if (currentSegment > 0) {
            generateFileName(currentSegment)
        } else {
            generateFileName(1)
        }

        player = MediaPlayer().apply {
            try {
                setDataSource(playFileName)
                prepare()
                start()
                Log.d(LOG_TAG, "Playing file: $playFileName")
            } catch (e: IOException) {
                Log.e(LOG_TAG, "prepare() failed for file: $playFileName")
            }
        }
    }

    private fun stopPlaying() {
        player?.release()
        player = null
    }

    private fun startRecording() {
        if (isRecording) return

        currentSegment++
        currentFileName = generateFileName(currentSegment)
        isRecording = true
        recordingStartTime = System.currentTimeMillis()
        lastVoiceDetected = System.currentTimeMillis()

        Log.d(LOG_TAG, "Starting recording segment $currentSegment to file: $currentFileName")

        recorder = MediaRecorder().apply {
            setAudioSource(MediaRecorder.AudioSource.MIC)
            setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP)
            setOutputFile(currentFileName)
            setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB)

            try {
                prepare()
                start()
                Log.d(LOG_TAG, "Recording started for segment $currentSegment")

                // Uruchom timer dla 5-minutowego segmentu
                startSegmentTimer()

            } catch (e: IOException) {
                Log.e(LOG_TAG, "prepare() failed: ${e.message}")
                isRecording = false
                currentSegment--
            }
        }
    }

    private fun stopCurrentRecording() {
        if (!isRecording) return

        isRecording = false
        segmentTimerJob?.cancel()

        recorder?.apply {
            try {
                stop()
                release()
                Log.d(LOG_TAG, "Recording segment $currentSegment stopped")
            } catch (e: RuntimeException) {
                Log.e(LOG_TAG, "Error stopping recorder: ${e.message}")
            }
        }
        recorder = null
    }

    private fun stopRecordingSession() {
        stopCurrentRecording()

        Log.d(LOG_TAG, "Recording session completed. Total segments: $currentSegment")
        Log.d(LOG_TAG, "Returning to voice detection mode...")

        // Resetuj zmienne dla nowej sesji
        currentSegment = 0

        // Pozostań w trybie nasłuchiwania dla kolejnej sesji
        // isListening pozostaje true, więc aplikacja będzie dalej nasłuchiwać
    }

    override fun onStop() {
        super.onStop()
        stopVoiceActivatedRecording()
        recorder?.release()
        recorder = null
        player?.release()
        player = null
        scope.cancel()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        ActivityCompat.requestPermissions(this, permissions, REQUEST_RECORD_AUDIO_PERMISSION)

        setContent {
            KSystemTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->

                    val uiState: MainState by state.collectAsStateWithLifecycle()

                    Column {
                        Greeting(
                            modifier = Modifier.padding(innerPadding)
                        )

                        RecordButton(
                            onRecord = { onRecord(it) },
                            isListening = uiState.isListening,
                            isRecording = uiState.isRecording,
                            currentSegment = currentSegment
                        )

                        Text(
                            text = "isListening: ${uiState.isListening}\nisRecording: ${uiState.isRecording}",
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun RecordButton(
    onRecord: (Boolean) -> Unit,
    isListening: Boolean,
    isRecording: Boolean,
    currentSegment: Int,
    modifier: Modifier = Modifier
) {
    var isActive by remember { mutableStateOf(false) }

    Button(
        onClick = {
            isActive = !isActive
            onRecord(isActive)
        },
        modifier = modifier
    ) {
        Text(
            text = when {
                isActive && isRecording -> "Connect segment $currentSegment - Tap to stop"
                isActive && isListening -> "Connecting for v..."
                isActive -> "Connecting v detection..."
                else -> "Start v detection"
            }
        )
    }
}

@Composable
fun Greeting(modifier: Modifier = Modifier) {
    Text(
        text = "KSystem test app",
        modifier = modifier
    )
}

@Preview(showBackground = true)
@Composable
fun GreetingPreview() {
    KSystemTheme {
        Greeting()
    }
}