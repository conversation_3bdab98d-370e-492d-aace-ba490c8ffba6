package pl.lazicki.ksystem

import org.junit.Test
import org.junit.Assert.*

/**
 * Test podstawowej funkcjonalności AudioRecordingService
 */
class AudioRecordingServiceTest {

    @Test
    fun mainState_initialValues_areCorrect() {
        val state = MainState()
        assertFalse("Initial isListening should be false", state.isListening)
        assertFalse("Initial isRecording should be false", state.isRecording)
    }

    @Test
    fun mainState_copyWithListening_updatesCorrectly() {
        val state = MainState()
        val updatedState = state.copy(isListening = true)
        
        assertTrue("isListening should be true", updatedState.isListening)
        assertFalse("isRecording should remain false", updatedState.isRecording)
    }

    @Test
    fun mainState_copyWithRecording_updatesCorrectly() {
        val state = MainState()
        val updatedState = state.copy(isRecording = true)
        
        assertFalse("isListening should remain false", updatedState.isListening)
        assertTrue("isRecording should be true", updatedState.isRecording)
    }

    @Test
    fun mainState_copyWithBoth_updatesCorrectly() {
        val state = MainState()
        val updatedState = state.copy(isListening = true, isRecording = true)
        
        assertTrue("isListening should be true", updatedState.isListening)
        assertTrue("isRecording should be true", updatedState.isRecording)
    }
}
