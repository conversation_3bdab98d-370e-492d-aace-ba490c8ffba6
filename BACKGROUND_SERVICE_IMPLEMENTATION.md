# Implementacja Serwisu w Tle dla KSystem

## Przegląd zmian

Aplikacja KSystem została zmodyfikowana, aby umo<PERSON><PERSON><PERSON><PERSON> pracę w tle jako serwis. Zachowano wszystkie obecne funkcjonalności nagrywania audio z aktywacją głosową, ale teraz aplikacja może działać w tle nawet po minimalizacji.

## Nowe komponenty

### 1. AudioRecordingService.kt
- **Typ**: Foreground Service z typem `microphone`
- **Funkcjonalność**: Przeniesiona cała logika nagrywania audio z MainActivity
- **Cechy**:
  - Wyświetla stałe powiadomienie podczas działania
  - Obsługuje voice activation (wykrywanie głosu)
  - Automatyczne dzielenie nagrań na 5-minutowe segmenty
  - Zatrzymywanie po 5 minutach ciszy
  - Może być kontrolowany przez Intent actions lub bound service

### 2. ServiceConnection.kt (AudioRecordingServiceConnection)
- **Funkcjonalność**: Zarządza połączeniem między MainActivity a AudioRecordingService
- **Cechy**:
  - Bound service connection
  - Przekazywanie komend do serwisu
  - Monitorowanie stanu połączenia

### 3. Zaktualizowany AndroidManifest.xml
Dodane uprawnienia:
- `FOREGROUND_SERVICE`
- `FOREGROUND_SERVICE_MICROPHONE`
- `POST_NOTIFICATIONS`
- Deklaracja serwisu z `foregroundServiceType="microphone"`

## Zmodyfikowane komponenty

### MainActivity.kt
- **Uproszczona**: Teraz pełni rolę kontrolera UI
- **Usunięte**: Cała logika nagrywania audio
- **Dodane**: Komunikacja z serwisem przez AudioRecordingServiceConnection
- **Zachowane**: UI, obsługa uprawnień, wyświetlanie stanu

## Jak to działa

### Uruchamianie nagrywania
1. Użytkownik naciska przycisk w MainActivity
2. MainActivity wywołuje `serviceConnection.startRecording()`
3. ServiceConnection uruchamia AudioRecordingService jako foreground service
4. Serwis wyświetla powiadomienie i rozpoczyna nasłuchiwanie głosu

### Praca w tle
1. Użytkownik może zminimalizować aplikację
2. AudioRecordingService kontynuuje pracę w tle
3. Powiadomienie informuje o stanie serwisu
4. Nagrywanie działa normalnie bez względu na stan aplikacji

### Zatrzymywanie
1. Użytkownik może zatrzymać przez UI aplikacji lub powiadomienie
2. Serwis zatrzymuje nagrywanie i usuwa powiadomienie
3. Serwis kończy działanie

## Korzyści

### ✅ Zachowane funkcjonalności
- Voice activation (wykrywanie głosu)
- Automatyczne segmentowanie nagrań (5 minut)
- Zatrzymywanie po ciszy (5 minut)
- Wszystkie parametry audio pozostały bez zmian

### ✅ Nowe możliwości
- **Praca w tle**: Aplikacja nagrywa nawet po minimalizacji
- **Powiadomienia**: Użytkownik widzi stan nagrywania
- **Stabilność**: Serwis jest odporny na zabijanie przez system
- **Kontrola**: Można zatrzymać nagrywanie z powiadomienia

### ✅ Zgodność z Android
- Używa Foreground Service zgodnie z wytycznymi Android
- Poprawne uprawnienia dla mikrofonu w tle
- Obsługa powiadomień zgodna z nowymi wersjami Android

## Testowanie

### Testy jednostkowe
- Dodano `AudioRecordingServiceTest.kt`
- Testuje podstawową funkcjonalność MainState
- Wszystkie testy przechodzą pomyślnie

### Kompilacja i instalacja
- ✅ Aplikacja kompiluje się bez błędów
- ✅ Można zainstalować na urządzeniu
- ⚠️ Ostrzeżenia o deprecated API (MediaRecorder, onRequestPermissionsResult)

## Użycie

1. **Uruchom aplikację** - przyznaj uprawnienia do mikrofonu
2. **Naciśnij "Start voice detection"** - rozpocznie się nasłuchiwanie
3. **Zminimalizuj aplikację** - nagrywanie będzie kontynuowane w tle
4. **Sprawdź powiadomienie** - pokazuje aktualny stan
5. **Zatrzymaj przez aplikację lub powiadomienie** - kończy nagrywanie

## Pliki nagrań

Nagrania są nadal zapisywane w `externalCacheDir` z formatem nazwy:
`ddMM_HH.mm_segX.3gp`

Gdzie:
- `ddMM` - dzień i miesiąc
- `HH.mm` - godzina i minuta
- `X` - numer segmentu

## Uwagi techniczne

- Serwis używa `START_STICKY` - system będzie próbował go zrestartować
- Powiadomienie ma priorytet `IMPORTANCE_LOW` - nie przeszkadza użytkownikowi
- Używa coroutines dla asynchronicznych operacji
- Bound service pozwala na bezpośrednią komunikację z UI
