# Naprawa Nagrywania w Tle - Rozwiązanie Problemu z Blokadą Ekranu

## Problem
Nagrywanie w tle przestawało działać po zablokowaniu telefonu, mimo że serwis pozostawał aktywny.

## Rozwiązanie

### 1. Wake Lock (PARTIAL_WAKE_LOCK)
**Dodano**: Mechanizm utrzymywania CPU w stanie aktywnym podczas nagrywania
- `PowerManager.PARTIAL_WAKE_LOCK` - utrzymuje CPU aktywne bez włączania ekranu
- Automatyczne zarządzanie: aktywacja przy starcie nagrywania, zwolnienie przy zatrzymaniu
- Timeout 10 minut dla bezpieczeństwa
- Obsługa błędów przy acquire/release

### 2. Optymalizacja AudioRecord
**Zmiany w konfiguracji audio**:
- <PERSON><PERSON><PERSON> z `MIC` na `VOICE_RECOGNITION` - lepsze dla rozpoznawania głosu
- Obniżenie częstotliwości próbkowania z 44100 Hz na 16000 Hz - mniejsze zużycie zasobów
- Zwiększenie bufora do 4x minimalnego rozmiaru - większa stabilność
- Zmniejszenie delay w pętli z 100ms na 50ms - lepsza responsywność

### 3. Obsługa Zdarzeń Systemowych
**Nowy komponent**: `ScreenStateReceiver`
- Monitoruje `ACTION_SCREEN_OFF`, `ACTION_SCREEN_ON`, `ACTION_USER_PRESENT`
- Automatycznie wzmacnia Wake Lock przy wyłączeniu ekranu podczas nagrywania
- Logowanie zdarzeń dla debugowania

### 4. Ulepszona Obsługa Błędów
**Dodano**:
- Licznik kolejnych błędów odczytu audio
- Automatyczne zatrzymanie po 10 kolejnych błędach
- Szczegółowe logowanie błędów
- Graceful handling wyjątków w pętli audio

### 5. Optymalizacja Priorytetu Wątku
**Dodano**:
- `Process.THREAD_PRIORITY_URGENT_AUDIO` dla wątku nagrywania
- Wyższy priorytet zapewnia stabilność podczas blokady ekranu
- Lepsze zarządzanie zasobami systemowymi

### 6. Nowe Uprawnienia
**Dodano w AndroidManifest.xml**:
```xml
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
```

## Jak Testować

### Test 1: Podstawowe Nagrywanie w Tle
1. Uruchom aplikację
2. Naciśnij "Start voice detection"
3. Zminimalizuj aplikację
4. Powiedz coś - sprawdź czy nagrywanie się rozpoczyna
5. Sprawdź powiadomienie - powinno pokazywać "Nagrywanie segmentu X"

### Test 2: Nagrywanie z Blokadą Ekranu
1. Uruchom nagrywanie jak w Test 1
2. **Zablokuj telefon** (przycisk power)
3. Poczekaj 10-15 sekund
4. **Powiedz coś głośno** w pobliżu mikrofonu
5. Odblokuj telefon i sprawdź:
   - Czy powiadomienie nadal pokazuje nagrywanie
   - Czy w logach widać "Voice detected"
   - Czy powstały pliki nagrań

### Test 3: Długotrwałe Nagrywanie
1. Uruchom nagrywanie
2. Zablokuj telefon na 10+ minut
3. Co kilka minut powiedz coś
4. Sprawdź czy:
   - Segmenty są tworzone co 5 minut
   - Wake Lock jest aktywny
   - Brak błędów w logach

### Test 4: Zarządzanie Zasobami
1. Uruchom nagrywanie
2. Sprawdź zużycie baterii w ustawieniach
3. Zatrzymaj nagrywanie
4. Sprawdź czy Wake Lock został zwolniony

## Monitorowanie przez ADB

### Sprawdzenie Wake Locks
```bash
adb shell dumpsys power | grep -i "wake lock"
```

### Sprawdzenie Logów
```bash
adb logcat | grep -E "(AudioRecordingService|ScreenStateReceiver)"
```

### Sprawdzenie Serwisów
```bash
adb shell dumpsys activity services | grep KSystem
```

## Oczekiwane Zachowanie

### ✅ Po Implementacji
- **Nagrywanie kontynuowane** po zablokowaniu ekranu
- **Wake Lock aktywny** podczas nagrywania
- **Automatyczne wykrywanie głosu** działa w tle
- **Segmentowanie** działa normalnie (5 min)
- **Timeout ciszy** działa normalnie (5 min)
- **Powiadomienie** aktualizuje się poprawnie

### ⚠️ Uwagi
- Zwiększone zużycie baterii (Wake Lock)
- Możliwe ograniczenia na niektórych wersjach Android
- Producenci mogą mieć dodatkowe ograniczenia (Samsung, Xiaomi, etc.)

## Dodatkowe Optymalizacje

### Dla Produkcji
1. **Whitelist aplikacji** w ustawieniach baterii
2. **Wyłączenie optymalizacji baterii** dla aplikacji
3. **Dodanie do autostart** (jeśli dostępne)
4. **Instrukcje dla użytkowników** różnych producentów

### Możliwe Rozszerzenia
1. **Adaptacyjny Wake Lock** - tylko gdy potrzebny
2. **Inteligentne wykrywanie ciszy** - ML model
3. **Kompresja audio w czasie rzeczywistym**
4. **Synchronizacja z chmurą**

## Pliki Zmodyfikowane

1. `AudioRecordingService.kt` - główne zmiany
2. `ScreenStateReceiver.kt` - nowy komponent
3. `AndroidManifest.xml` - nowe uprawnienia
4. `BACKGROUND_RECORDING_FIX.md` - dokumentacja

## Testowanie Zakończone

- ✅ Kompilacja bez błędów
- ✅ Instalacja na urządzeniu
- ✅ Podstawowe funkcje działają
- 🔄 **Wymagane**: Test z blokadą ekranu na prawdziwym urządzeniu
